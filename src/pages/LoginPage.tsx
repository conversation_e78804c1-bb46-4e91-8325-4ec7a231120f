import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import {
  Container,
  Paper,
  Box,
  Typography,
  Button,
  Alert,
  Avatar,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import LockOutlinedIcon from '@mui/icons-material/LockOutlined';
import { useAuth } from '@/hooks/useAuth';
import FormField from '@/components/forms/FormField';
import LoadingSpinner from '@/components/common/LoadingSpinner';
import { LoginRequest } from '@/types';

const LoginContainer = styled(Container)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  minHeight: 'calc(100vh - 200px)',
}));

const LoginPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  maxWidth: 400,
  width: '100%',
}));

const LoginForm = styled('form')(({ theme }) => ({
  width: '100%',
  marginTop: theme.spacing(1),
}));

const LoginPage: React.FC = () => {
  const { login, isLoading, error, clearError } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [submitError, setSubmitError] = useState<string | null>(null);

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<LoginRequest>({
    defaultValues: {
      user_id: '',
      user_passwd: '',
    },
  });

  const from = (location.state as any)?.from?.pathname || '/list';

  // 입력 필드 변경 시 에러 초기화
  const watchedFields = watch();
  React.useEffect(() => {
    if (submitError || error) {
      setSubmitError(null);
      clearError();
    }
  }, [
    watchedFields.user_id,
    watchedFields.user_passwd,
    submitError,
    error,
    clearError,
  ]);

  const onSubmit = async (data: LoginRequest) => {
    try {
      setSubmitError(null);
      clearError();
      await login(data);
      navigate(from, { replace: true });
    } catch (error: any) {
      setSubmitError(error.message || '로그인에 실패했습니다.');
    }
  };

  if (isLoading) {
    return <LoadingSpinner fullScreen message="로그인 중..." />;
  }

  return (
    <LoginContainer maxWidth="sm">
      <LoginPaper elevation={3}>
        <Avatar sx={{ m: 1, bgcolor: 'secondary.main' }}>
          <LockOutlinedIcon />
        </Avatar>

        <Typography component="h1" variant="h5" gutterBottom>
          로그인
        </Typography>

        {(error || submitError) && (
          <Alert severity="error" sx={{ width: '100%', mb: 2 }}>
            {error || submitError}
          </Alert>
        )}

        <LoginForm onSubmit={handleSubmit(onSubmit)}>
          <FormField
            name="user_id"
            control={control}
            label="사용자 ID"
            variant="outlined"
            margin="normal"
            required
            autoFocus
            rules={{
              required: '사용자 ID를 입력해주세요.',
              minLength: {
                value: 4,
                message: '사용자 ID는 4자 이상이어야 합니다.',
              },
            }}
          />

          <FormField
            name="user_passwd"
            control={control}
            label="비밀번호"
            type="password"
            variant="outlined"
            margin="normal"
            required
            rules={{
              required: '비밀번호를 입력해주세요.',
              minLength: {
                value: 4,
                message: '비밀번호는 4자 이상이어야 합니다.',
              },
            }}
          />

          <Button
            type="submit"
            fullWidth
            variant="contained"
            sx={{ mt: 3, mb: 2 }}
            disabled={isLoading}
          >
            로그인
          </Button>

          <Box textAlign="center">
            <Button
              component={Link}
              to="/register"
              variant="text"
              color="primary"
            >
              계정이 없으신가요? 회원가입
            </Button>
          </Box>
        </LoginForm>
      </LoginPaper>
    </LoginContainer>
  );
};

export default LoginPage;
